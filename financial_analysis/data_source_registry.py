"""
数据源注册器模块

提供数据源的动态注册、管理和配置功能。
支持数据源的增删改查、状态监控、配置验证等功能。
"""

import json
import os
from datetime import datetime
from typing import List, Optional, Dict, Any, Type
from loguru import logger

from .models import DataSourceConfig, DataSourceStatus, UnifiedNewsData
from .data_source_adapters import (
    DataSourceAdapter, 
    get_adapter_class, 
    register_adapter,
    list_adapter_types
)
from .config import settings


class DataSourceRegistry:
    """数据源注册器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化数据源注册器
        
        Args:
            config_file: 配置文件路径，默认使用环境变量配置
        """
        self.config_file = config_file or "data_sources.json"
        self._sources: Dict[str, DataSourceConfig] = {}
        self._adapters: Dict[str, DataSourceAdapter] = {}
        self._load_sources()
        
    def _load_sources(self):
        """加载数据源配置"""
        try:
            # 首先尝试从文件加载
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    sources_data = json.load(f)
                    
                for source_data in sources_data:
                    config = DataSourceConfig(**source_data)
                    self._sources[config.source_id] = config
                    
                logger.info(f"从文件加载了 {len(self._sources)} 个数据源配置")
            
            # 然后从环境变量加载（兼容现有配置）
            if settings.hot_news_channels:
                try:
                    channels_data = json.loads(settings.hot_news_channels)
                    for channel_data in channels_data:
                        # 转换旧格式到新格式
                        config = self._convert_legacy_channel(channel_data)
                        if config:
                            self._sources[config.source_id] = config
                            
                    logger.info(f"从环境变量加载了 {len(channels_data)} 个数据源配置")
                except json.JSONDecodeError as e:
                    logger.error(f"解析环境变量配置失败: {str(e)}")
            
            # 如果没有配置，使用默认配置
            if not self._sources:
                self._load_default_sources()
                
        except Exception as e:
            logger.error(f"加载数据源配置失败: {str(e)}")
            self._load_default_sources()
    
    def _convert_legacy_channel(self, channel_data: Dict[str, Any]) -> Optional[DataSourceConfig]:
        """转换旧的渠道配置到新格式"""
        try:
            # 确定适配器类型
            channel_type = channel_data.get('channel_type', 'rss')
            adapter_type_map = {
                'rss': 'rss',
                'api': 'api', 
                'web': 'web',
                'social': 'api'  # 社交媒体通常使用API
            }
            adapter_type = adapter_type_map.get(channel_type, 'rss')
            
            # 构建配置
            config_dict = {
                'url': channel_data.get('url'),
                'headers': channel_data.get('headers', {}),
                'params': channel_data.get('params', {}),
                'api_key': channel_data.get('api_key')
            }
            
            return DataSourceConfig(
                source_id=channel_data['channel_id'],
                name=channel_data['name'],
                source_type=adapter_type,
                adapter_class=f"{adapter_type.title()}Adapter",
                config=config_dict,
                enabled=channel_data.get('enabled', True),
                priority=channel_data.get('priority', 1),
                fetch_interval=channel_data.get('fetch_interval', 300)
            )
            
        except Exception as e:
            logger.warning(f"转换旧配置失败: {str(e)}")
            return None
    
    def _load_default_sources(self):
        """加载默认数据源"""
        default_sources = [
            DataSourceConfig(
                source_id="sina_finance_rss",
                name="新浪财经RSS",
                source_type="rss",
                adapter_class="RSSAdapter",
                config={
                    "url": "https://feed.mix.sina.com.cn/api/roll/get?pageid=153&lid=1686&k=&num=50&page=1"
                },
                enabled=True,
                priority=1,
                fetch_interval=300
            ),
            DataSourceConfig(
                source_id="163_money_rss",
                name="网易财经RSS",
                source_type="rss", 
                adapter_class="RSSAdapter",
                config={
                    "url": "http://money.163.com/special/002557S6/rss_jsxw.xml"
                },
                enabled=True,
                priority=2,
                fetch_interval=300
            ),
            DataSourceConfig(
                source_id="eastmoney_news",
                name="东方财富快讯",
                source_type="api",
                adapter_class="APIAdapter",
                config={
                    "url": "https://np-anotice-stock.eastmoney.com/api/security/ann",
                    "params": {"page_size": 50, "page_index": 1}
                },
                enabled=True,
                priority=3,
                fetch_interval=300
            )
        ]
        
        for config in default_sources:
            self._sources[config.source_id] = config
            
        logger.info(f"加载了 {len(default_sources)} 个默认数据源")
    
    def _save_sources(self):
        """保存数据源配置到文件"""
        try:
            sources_data = []
            for config in self._sources.values():
                sources_data.append(config.dict())
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(sources_data, f, ensure_ascii=False, indent=2, default=str)
                
            logger.info(f"保存了 {len(sources_data)} 个数据源配置到文件")
            
        except Exception as e:
            logger.error(f"保存数据源配置失败: {str(e)}")
    
    def register_source(self, config: DataSourceConfig) -> bool:
        """
        注册新的数据源
        
        Args:
            config: 数据源配置
            
        Returns:
            是否注册成功
        """
        try:
            # 验证配置
            if not self._validate_source_config(config):
                return False
            
            # 检查是否已存在
            if config.source_id in self._sources:
                logger.warning(f"数据源 {config.source_id} 已存在，将被覆盖")
            
            # 更新时间戳
            config.updated_time = datetime.now()
            if config.source_id not in self._sources:
                config.created_time = datetime.now()
            
            # 注册数据源
            self._sources[config.source_id] = config
            
            # 创建适配器实例
            self._create_adapter(config)
            
            # 保存配置
            self._save_sources()
            
            logger.info(f"成功注册数据源: {config.source_id}")
            return True
            
        except Exception as e:
            logger.error(f"注册数据源失败: {str(e)}")
            return False
    
    def unregister_source(self, source_id: str) -> bool:
        """
        注销数据源
        
        Args:
            source_id: 数据源ID
            
        Returns:
            是否注销成功
        """
        try:
            if source_id not in self._sources:
                logger.warning(f"数据源 {source_id} 不存在")
                return False
            
            # 移除配置和适配器
            del self._sources[source_id]
            if source_id in self._adapters:
                del self._adapters[source_id]
            
            # 保存配置
            self._save_sources()
            
            logger.info(f"成功注销数据源: {source_id}")
            return True
            
        except Exception as e:
            logger.error(f"注销数据源失败: {str(e)}")
            return False
    
    def update_source(self, source_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新数据源配置
        
        Args:
            source_id: 数据源ID
            updates: 更新的配置项
            
        Returns:
            是否更新成功
        """
        try:
            if source_id not in self._sources:
                logger.warning(f"数据源 {source_id} 不存在")
                return False
            
            # 更新配置
            config = self._sources[source_id]
            for key, value in updates.items():
                if hasattr(config, key):
                    setattr(config, key, value)
                elif key == 'config':
                    # 更新配置字典
                    config.config.update(value)
            
            config.updated_time = datetime.now()
            
            # 重新创建适配器
            self._create_adapter(config)
            
            # 保存配置
            self._save_sources()
            
            logger.info(f"成功更新数据源: {source_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新数据源失败: {str(e)}")
            return False
    
    def get_source(self, source_id: str) -> Optional[DataSourceConfig]:
        """
        获取数据源配置
        
        Args:
            source_id: 数据源ID
            
        Returns:
            数据源配置
        """
        return self._sources.get(source_id)
    
    def list_sources(self, enabled_only: bool = False) -> List[DataSourceConfig]:
        """
        列出所有数据源
        
        Args:
            enabled_only: 是否只返回启用的数据源
            
        Returns:
            数据源配置列表
        """
        sources = list(self._sources.values())
        
        if enabled_only:
            sources = [s for s in sources if s.enabled]
        
        # 按优先级排序
        sources.sort(key=lambda x: x.priority)
        return sources
    
    def get_adapter(self, source_id: str) -> Optional[DataSourceAdapter]:
        """
        获取数据源适配器
        
        Args:
            source_id: 数据源ID
            
        Returns:
            数据源适配器
        """
        if source_id not in self._adapters:
            config = self._sources.get(source_id)
            if config:
                self._create_adapter(config)
        
        return self._adapters.get(source_id)
    
    def _create_adapter(self, config: DataSourceConfig) -> Optional[DataSourceAdapter]:
        """创建适配器实例"""
        try:
            adapter_class = get_adapter_class(config.source_type)
            adapter = adapter_class(config)
            
            # 验证适配器配置
            if not adapter.validate_config():
                logger.error(f"数据源 {config.source_id} 配置验证失败")
                return None
            
            self._adapters[config.source_id] = adapter
            return adapter
            
        except Exception as e:
            logger.error(f"创建适配器失败: {str(e)}")
            return None
    
    def _validate_source_config(self, config: DataSourceConfig) -> bool:
        """验证数据源配置"""
        try:
            # 检查必填字段
            if not config.source_id or not config.name:
                logger.error("数据源ID和名称不能为空")
                return False
            
            # 检查适配器类型
            if config.source_type not in list_adapter_types():
                logger.error(f"不支持的适配器类型: {config.source_type}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证配置失败: {str(e)}")
            return False
    
    def get_source_status(self, source_id: str) -> Optional[DataSourceStatus]:
        """
        获取数据源状态
        
        Args:
            source_id: 数据源ID
            
        Returns:
            数据源状态
        """
        adapter = self.get_adapter(source_id)
        if adapter:
            return adapter.get_status()
        return None
    
    def test_source(self, source_id: str) -> Dict[str, Any]:
        """
        测试数据源连接
        
        Args:
            source_id: 数据源ID
            
        Returns:
            测试结果
        """
        try:
            adapter = self.get_adapter(source_id)
            if not adapter:
                return {"success": False, "error": "适配器不存在"}
            
            # 尝试获取数据
            start_time = datetime.now()
            data = adapter.fetch_with_retry()
            end_time = datetime.now()
            
            response_time = (end_time - start_time).total_seconds()
            
            return {
                "success": True,
                "data_count": len(data),
                "response_time": response_time,
                "status": adapter.get_status().dict()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}


# 全局数据源注册器实例
data_source_registry = DataSourceRegistry()
