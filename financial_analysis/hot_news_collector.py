"""
热点信息收集器模块

支持从多个渠道获取热点信息，包括RSS订阅、新闻API、网页爬取等。
提供统一的接口进行热点信息的收集、处理和存储。

新版本集成了动态数据源管理功能，支持统一的数据格式和适配器模式。
"""

import json
import time
import hashlib
import feedparser
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import requests
from loguru import logger

from .models import HotNewsItem, HotNewsChannel, UnifiedNewsData
from .config import settings
from .utils import safe_json_parse
from .data_source_registry import data_source_registry


class HotNewsCollector:
    """热点信息收集器类"""

    def __init__(self, use_new_system: bool = True):
        """
        初始化热点信息收集器

        Args:
            use_new_system: 是否使用新的数据源管理系统
        """
        self.use_new_system = use_new_system

        if use_new_system:
            # 使用新的数据源管理系统
            self.registry = data_source_registry
            logger.info("使用新的数据源管理系统")
        else:
            # 保持向后兼容，使用旧系统
            self._channels = self._load_channels()
            self._session = requests.Session()
            self._session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            logger.info("使用传统数据源管理系统")

        logger.info("热点信息收集器初始化完成")
    
    def collect_hot_news(self, force_refresh: bool = False) -> List[HotNewsItem]:
        """
        收集热点信息

        Args:
            force_refresh: 是否强制刷新，忽略获取间隔限制

        Returns:
            热点信息列表
        """
        if self.use_new_system:
            return self._collect_with_new_system(force_refresh)
        else:
            return self._collect_with_legacy_system(force_refresh)

    def _collect_with_new_system(self, force_refresh: bool = False) -> List[HotNewsItem]:
        """使用新数据源管理系统收集热点信息"""
        try:
            all_unified_news = []
            current_time = datetime.now()

            # 获取启用的数据源
            sources = self.registry.list_sources(enabled_only=True)

            for source in sources:
                # 检查获取间隔
                if not force_refresh and source.last_fetch_time:
                    time_diff = (current_time - source.last_fetch_time).total_seconds()
                    if time_diff < source.fetch_interval:
                        logger.debug(f"跳过数据源 {source.name}，未到获取时间")
                        continue

                logger.info(f"从数据源 {source.name} 获取热点信息")

                try:
                    # 获取适配器并获取数据
                    adapter = self.registry.get_adapter(source.source_id)
                    if not adapter:
                        logger.error(f"无法获取数据源 {source.source_id} 的适配器")
                        continue

                    unified_news = adapter.fetch_with_retry()
                    if unified_news:
                        all_unified_news.extend(unified_news)
                        logger.info(f"从 {source.name} 获取到 {len(unified_news)} 条信息")

                    # 更新最后获取时间
                    source.last_fetch_time = current_time

                except Exception as e:
                    logger.error(f"从数据源 {source.name} 获取信息失败: {str(e)}")
                    continue

            # 转换为HotNewsItem格式（保持向后兼容）
            hot_news_items = self._convert_unified_to_hot_news(all_unified_news)

            # 去重和排序
            unique_news = self._deduplicate_news(hot_news_items)
            sorted_news = sorted(unique_news, key=lambda x: x.publish_time, reverse=True)

            # 限制数量
            max_items = settings.hot_news_max_items
            if len(sorted_news) > max_items:
                sorted_news = sorted_news[:max_items]

            logger.info(f"总共收集到 {len(sorted_news)} 条热点信息")
            return sorted_news

        except Exception as e:
            logger.error(f"收集热点信息失败: {str(e)}")
            return []

    def _collect_with_legacy_system(self, force_refresh: bool = False) -> List[HotNewsItem]:
        """使用传统系统收集热点信息（保持向后兼容）"""
        try:
            all_news = []
            current_time = datetime.now()

            for channel in self._channels:
                if not channel.enabled:
                    continue

                # 检查获取间隔
                if not force_refresh and channel.last_fetch_time:
                    time_diff = (current_time - channel.last_fetch_time).total_seconds()
                    if time_diff < channel.fetch_interval:
                        logger.debug(f"跳过渠道 {channel.name}，未到获取时间")
                        continue

                logger.info(f"从渠道 {channel.name} 获取热点信息")

                try:
                    news_items = self._fetch_from_channel(channel)
                    if news_items:
                        all_news.extend(news_items)
                        logger.info(f"从 {channel.name} 获取到 {len(news_items)} 条信息")

                    # 更新最后获取时间
                    channel.last_fetch_time = current_time

                except Exception as e:
                    logger.error(f"从渠道 {channel.name} 获取信息失败: {str(e)}")
                    continue

            # 去重和排序
            unique_news = self._deduplicate_news(all_news)
            sorted_news = sorted(unique_news, key=lambda x: x.publish_time, reverse=True)

            # 限制数量
            max_items = settings.hot_news_max_items
            if len(sorted_news) > max_items:
                sorted_news = sorted_news[:max_items]

            logger.info(f"总共收集到 {len(sorted_news)} 条热点信息")
            return sorted_news

        except Exception as e:
            logger.error(f"收集热点信息失败: {str(e)}")
            return []
    
    def _load_channels(self) -> List[HotNewsChannel]:
        """加载数据源渠道配置"""
        try:
            if not settings.hot_news_channels:
                return self._get_default_channels()
            
            channels_data = json.loads(settings.hot_news_channels)
            channels = []
            
            for channel_data in channels_data:
                channel = HotNewsChannel(**channel_data)
                channels.append(channel)
            
            logger.info(f"加载了 {len(channels)} 个数据源渠道")
            return channels
            
        except Exception as e:
            logger.error(f"加载渠道配置失败: {str(e)}")
            return self._get_default_channels()
    
    def _get_default_channels(self) -> List[HotNewsChannel]:
        """获取默认的数据源渠道"""
        default_channels = [
            HotNewsChannel(
                channel_id="sina_finance_rss",
                name="新浪财经RSS",
                channel_type="rss",
                url="https://feed.mix.sina.com.cn/api/roll/get?pageid=153&lid=1686&k=&num=50&page=1",
                enabled=True,
                priority=1,
                fetch_interval=300
            ),
            HotNewsChannel(
                channel_id="163_money_rss",
                name="网易财经RSS",
                channel_type="rss",
                url="http://money.163.com/special/002557S6/rss_jsxw.xml",
                enabled=True,
                priority=2,
                fetch_interval=300
            ),
            HotNewsChannel(
                channel_id="eastmoney_news",
                name="东方财富快讯",
                channel_type="api",
                url="https://np-anotice-stock.eastmoney.com/api/security/ann",
                enabled=True,
                priority=3,
                fetch_interval=300,
                params={"page_size": 50, "page_index": 1}
            )
        ]
        
        logger.info(f"使用默认的 {len(default_channels)} 个数据源渠道")
        return default_channels
    
    def _fetch_from_channel(self, channel: HotNewsChannel) -> List[HotNewsItem]:
        """从指定渠道获取信息"""
        try:
            if channel.channel_type == "rss":
                return self._fetch_from_rss(channel)
            elif channel.channel_type == "api":
                return self._fetch_from_api(channel)
            elif channel.channel_type == "web":
                return self._fetch_from_web(channel)
            else:
                logger.warning(f"不支持的渠道类型: {channel.channel_type}")
                return []
                
        except Exception as e:
            logger.error(f"从渠道 {channel.name} 获取信息失败: {str(e)}")
            return []
    
    def _fetch_from_rss(self, channel: HotNewsChannel) -> List[HotNewsItem]:
        """从RSS源获取信息"""
        try:
            feed = feedparser.parse(channel.url)
            news_items = []
            current_time = datetime.now()
            
            for entry in feed.entries:
                try:
                    # 解析发布时间
                    publish_time = self._parse_publish_time(entry)
                    if not publish_time:
                        publish_time = current_time
                    
                    # 生成新闻ID
                    news_id = self._generate_news_id(entry.title, entry.link if hasattr(entry, 'link') else "")
                    
                    # 创建新闻条目
                    news_item = HotNewsItem(
                        news_id=news_id,
                        title=entry.title,
                        content=getattr(entry, 'description', None),
                        source=channel.name,
                        channel_id=channel.channel_id,
                        url=getattr(entry, 'link', None),
                        publish_time=publish_time,
                        fetch_time=current_time
                    )
                    
                    news_items.append(news_item)
                    
                except Exception as e:
                    logger.warning(f"解析RSS条目失败: {str(e)}")
                    continue
            
            return news_items
            
        except Exception as e:
            logger.error(f"获取RSS信息失败: {str(e)}")
            return []
    
    def _fetch_from_api(self, channel: HotNewsChannel) -> List[HotNewsItem]:
        """从API获取信息"""
        try:
            headers = channel.headers or {}
            if channel.api_key:
                headers['Authorization'] = f'Bearer {channel.api_key}'
            
            params = channel.params or {}
            
            response = self._session.get(
                channel.url,
                headers=headers,
                params=params,
                timeout=30
            )
            response.raise_for_status()

            # 使用安全JSON解析
            data = safe_json_parse(response, f"热点新闻API({channel.name})")
            if data is None:
                return []

            return self._parse_api_response(data, channel)
            
        except Exception as e:
            logger.error(f"API请求失败: {str(e)}")
            return []
    
    def _fetch_from_web(self, channel: HotNewsChannel) -> List[HotNewsItem]:
        """从网页爬取信息"""
        try:
            # 这里可以实现网页爬取逻辑
            # 由于网页结构复杂多变，这里提供基础框架
            logger.warning(f"网页爬取功能待实现: {channel.name}")
            return []
            
        except Exception as e:
            logger.error(f"网页爬取失败: {str(e)}")
            return []
    
    def _parse_api_response(self, data: Dict[str, Any], channel: HotNewsChannel) -> List[HotNewsItem]:
        """解析API响应数据"""
        try:
            news_items = []
            current_time = datetime.now()
            
            # 根据不同的API格式进行解析
            # 这里提供通用的解析逻辑，可以根据具体API调整
            items = data.get('data', data.get('items', data.get('results', [])))
            
            for item in items:
                try:
                    title = item.get('title', item.get('headline', ''))
                    if not title:
                        continue
                    
                    news_id = self._generate_news_id(title, item.get('url', ''))
                    
                    news_item = HotNewsItem(
                        news_id=news_id,
                        title=title,
                        content=item.get('content', item.get('description', '')),
                        source=channel.name,
                        channel_id=channel.channel_id,
                        url=item.get('url', item.get('link', '')),
                        publish_time=self._parse_publish_time(item) or current_time,
                        fetch_time=current_time,
                        view_count=item.get('view_count'),
                        comment_count=item.get('comment_count'),
                        share_count=item.get('share_count')
                    )
                    
                    news_items.append(news_item)
                    
                except Exception as e:
                    logger.warning(f"解析API条目失败: {str(e)}")
                    continue
            
            return news_items
            
        except Exception as e:
            logger.error(f"解析API响应失败: {str(e)}")
            return []
    
    def _parse_publish_time(self, item: Any) -> Optional[datetime]:
        """解析发布时间"""
        try:
            # 尝试多种时间字段
            time_fields = ['published', 'pubDate', 'publish_time', 'created_at', 'date']
            
            for field in time_fields:
                if hasattr(item, field):
                    time_str = getattr(item, field)
                elif isinstance(item, dict) and field in item:
                    time_str = item[field]
                else:
                    continue
                
                if time_str:
                    return self._parse_time_string(time_str)
            
            return None
            
        except Exception as e:
            logger.warning(f"解析发布时间失败: {str(e)}")
            return None
    
    def _parse_time_string(self, time_str: str) -> Optional[datetime]:
        """解析时间字符串"""
        try:
            # 尝试多种时间格式
            time_formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%d',
                '%a, %d %b %Y %H:%M:%S %Z',
                '%a, %d %b %Y %H:%M:%S %z'
            ]
            
            for fmt in time_formats:
                try:
                    return datetime.strptime(time_str, fmt)
                except ValueError:
                    continue
            
            # 如果都失败了，尝试使用dateutil解析
            try:
                from dateutil import parser
                return parser.parse(time_str)
            except:
                pass
            
            return None
            
        except Exception as e:
            logger.warning(f"解析时间字符串失败: {str(e)}")
            return None
    
    def _generate_news_id(self, title: str, url: str) -> str:
        """生成新闻唯一标识"""
        content = f"{title}_{url}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _deduplicate_news(self, news_items: List[HotNewsItem]) -> List[HotNewsItem]:
        """去重新闻"""
        seen_ids = set()
        unique_news = []
        
        for news in news_items:
            if news.news_id not in seen_ids:
                seen_ids.add(news.news_id)
                unique_news.append(news)
        
        return unique_news

    def _convert_unified_to_hot_news(self, unified_news: List[UnifiedNewsData]) -> List[HotNewsItem]:
        """将统一格式的新闻数据转换为HotNewsItem格式"""
        hot_news_items = []

        for news in unified_news:
            try:
                hot_news_item = HotNewsItem(
                    news_id=news.id,
                    title=news.title,
                    content=news.content,
                    summary=news.summary,
                    source=news.source_name,
                    channel_id=news.source_id,
                    url=news.original_url,
                    image_url=news.image_urls[0] if news.image_urls else None,
                    publish_time=news.publish_time or news.fetch_time,
                    fetch_time=news.fetch_time,
                    view_count=news.view_count,
                    comment_count=news.comment_count,
                    share_count=news.share_count,
                    category=news.category,
                    tags=news.tags,
                    keywords=news.keywords
                )
                hot_news_items.append(hot_news_item)

            except Exception as e:
                logger.warning(f"转换统一新闻数据失败: {str(e)}")
                continue

        return hot_news_items

    def collect_unified_news(self, force_refresh: bool = False) -> List[UnifiedNewsData]:
        """
        收集统一格式的热点信息

        Args:
            force_refresh: 是否强制刷新，忽略获取间隔限制

        Returns:
            统一格式的新闻数据列表
        """
        if not self.use_new_system:
            logger.warning("当前使用传统系统，无法返回统一格式数据")
            return []

        try:
            all_unified_news = []
            current_time = datetime.now()

            # 获取启用的数据源
            sources = self.registry.list_sources(enabled_only=True)

            for source in sources:
                # 检查获取间隔
                if not force_refresh and source.last_fetch_time:
                    time_diff = (current_time - source.last_fetch_time).total_seconds()
                    if time_diff < source.fetch_interval:
                        logger.debug(f"跳过数据源 {source.name}，未到获取时间")
                        continue

                logger.info(f"从数据源 {source.name} 获取热点信息")

                try:
                    # 获取适配器并获取数据
                    adapter = self.registry.get_adapter(source.source_id)
                    if not adapter:
                        logger.error(f"无法获取数据源 {source.source_id} 的适配器")
                        continue

                    unified_news = adapter.fetch_with_retry()
                    if unified_news:
                        all_unified_news.extend(unified_news)
                        logger.info(f"从 {source.name} 获取到 {len(unified_news)} 条信息")

                    # 更新最后获取时间
                    source.last_fetch_time = current_time

                except Exception as e:
                    logger.error(f"从数据源 {source.name} 获取信息失败: {str(e)}")
                    continue

            # 去重（基于ID）
            unique_news = self._deduplicate_unified_news(all_unified_news)

            # 按发布时间排序
            sorted_news = sorted(unique_news,
                               key=lambda x: x.publish_time or x.fetch_time,
                               reverse=True)

            # 限制数量
            max_items = settings.hot_news_max_items
            if len(sorted_news) > max_items:
                sorted_news = sorted_news[:max_items]

            logger.info(f"总共收集到 {len(sorted_news)} 条统一格式热点信息")
            return sorted_news

        except Exception as e:
            logger.error(f"收集统一格式热点信息失败: {str(e)}")
            return []

    def _deduplicate_unified_news(self, news_items: List[UnifiedNewsData]) -> List[UnifiedNewsData]:
        """去重统一格式新闻"""
        seen_ids = set()
        unique_news = []

        for news in news_items:
            if news.id not in seen_ids:
                seen_ids.add(news.id)
                unique_news.append(news)

        return unique_news

    def add_data_source(self, source_config: dict) -> bool:
        """
        动态添加数据源

        Args:
            source_config: 数据源配置字典

        Returns:
            是否添加成功
        """
        if not self.use_new_system:
            logger.error("当前使用传统系统，无法动态添加数据源")
            return False

        try:
            from .models import DataSourceConfig
            config = DataSourceConfig(**source_config)
            return self.registry.register_source(config)
        except Exception as e:
            logger.error(f"添加数据源失败: {str(e)}")
            return False

    def remove_data_source(self, source_id: str) -> bool:
        """
        移除数据源

        Args:
            source_id: 数据源ID

        Returns:
            是否移除成功
        """
        if not self.use_new_system:
            logger.error("当前使用传统系统，无法动态移除数据源")
            return False

        return self.registry.unregister_source(source_id)

    def update_data_source(self, source_id: str, updates: dict) -> bool:
        """
        更新数据源配置

        Args:
            source_id: 数据源ID
            updates: 更新的配置项

        Returns:
            是否更新成功
        """
        if not self.use_new_system:
            logger.error("当前使用传统系统，无法动态更新数据源")
            return False

        return self.registry.update_source(source_id, updates)

    def list_data_sources(self) -> List[dict]:
        """
        列出所有数据源

        Returns:
            数据源配置列表
        """
        if not self.use_new_system:
            # 返回传统格式
            return [channel.dict() for channel in self._channels]

        sources = self.registry.list_sources()
        return [source.dict() for source in sources]

    def get_data_source_status(self, source_id: str) -> Optional[dict]:
        """
        获取数据源状态

        Args:
            source_id: 数据源ID

        Returns:
            数据源状态信息
        """
        if not self.use_new_system:
            logger.error("当前使用传统系统，无法获取详细状态")
            return None

        status = self.registry.get_source_status(source_id)
        return status.dict() if status else None

    def test_data_source(self, source_id: str) -> dict:
        """
        测试数据源连接

        Args:
            source_id: 数据源ID

        Returns:
            测试结果
        """
        if not self.use_new_system:
            logger.error("当前使用传统系统，无法进行连接测试")
            return {"success": False, "error": "传统系统不支持连接测试"}

        return self.registry.test_source(source_id)
